{"total_questions": 18, "categories": {"Java基础面试题": {"count": 5, "description": "Java语言基础知识和核心概念", "icon": "☕"}, "Spring框架面试题": {"count": 3, "description": "Spring框架相关知识和应用", "icon": "🌱"}, "数据库面试题": {"count": 3, "description": "数据库设计、优化和管理相关知识", "icon": "🗄️"}, "算法与数据结构": {"count": 2, "description": "算法思维和数据结构相关面试题", "icon": "🧮"}, "操作系统面试题": {"count": 2, "description": "操作系统原理和系统编程相关知识", "icon": "💻"}, "前端开发面试题": {"count": 2, "description": "HTML、CSS、JavaScript等前端技术", "icon": "🌐"}, "项目经验面试题": {"count": 1, "description": "项目管理、团队协作和实际工作经验", "icon": "🚀"}}, "difficulty_distribution": {"中等": 13, "基础": 2, "高级": 3}, "tag_distribution": {"JVM": 1, "基础概念": 2, "面向对象": 1, "字符串": 1, "性能优化": 1, "集合框架": 1, "数据结构": 2, "异常处理": 1, "错误处理": 1, "Spring": 1, "IoC": 1, "DI": 1, "AOP": 2, "Spring Boot": 1, "微服务": 1, "自动配置": 1, "代理模式": 1, "切面编程": 1, "索引": 1, "数据库优化": 1, "事务": 1, "ACID": 1, "数据一致性": 1, "MySQL": 1, "存储引擎": 1, "InnoDB": 1, "排序算法": 1, "分治法": 1, "递归": 1, "二叉树": 1, "搜索": 1, "进程": 1, "线程": 1, "并发编程": 1, "死锁": 1, "并发控制": 1, "资源管理": 1, "JavaScript": 1, "闭包": 1, "作用域": 1, "CSS": 1, "盒模型": 1, "布局": 1, "项目管理": 1, "问题解决": 1, "团队协作": 1}}