#!/usr/bin/env python3
"""
微信小程序《面试官的宝典》抓取和电子书生成主程序

使用方法:
python main.py

功能:
1. 抓取微信小程序内容
2. 数据清洗和结构化
3. 生成多种格式的电子书
"""

import os
import sys
import argparse
from miniprogram_scraper import MiniprogramScraper
from book_generator import BookGenerator
from config import OUTPUT_CONFIG, BOOK_CONFIG

def print_banner():
    """打印程序横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    微信小程序内容抓取工具                      ║
    ║                  《面试官的宝典》电子书生成器                   ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_menu():
    """打印主菜单"""
    menu = """
    请选择操作:
    
    1. 抓取小程序内容
    2. 生成电子书 (需要先抓取内容)
    3. 完整流程 (抓取 + 生成)
    4. 查看已有数据
    5. 退出
    
    """
    print(menu)

def scrape_content():
    """抓取内容"""
    print("\n" + "="*50)
    print("开始抓取小程序内容...")
    print("="*50)
    
    scraper = MiniprogramScraper()
    content = scraper.scrape_content()
    
    if content:
        print(f"\n✅ 成功抓取 {len(content)} 条内容")
        return True
    else:
        print("\n❌ 抓取失败或无内容")
        return False

def generate_book():
    """生成电子书"""
    print("\n" + "="*50)
    print("开始生成电子书...")
    print("="*50)
    
    # 检查是否有原始数据
    raw_data_file = os.path.join(OUTPUT_CONFIG['raw_data_dir'], 'raw_data.json')
    if not os.path.exists(raw_data_file):
        print("❌ 未找到原始数据文件，请先抓取内容")
        return False
    
    generator = BookGenerator()
    
    if not generator.load_data(raw_data_file):
        print("❌ 加载数据失败")
        return False
    
    try:
        # 生成统计摘要
        summary = generator.generate_summary()
        
        print(f"\n📊 数据统计:")
        print(f"   总题目数: {summary['total_questions']}")
        print(f"   分类数: {len(summary['categories'])}")
        
        for category, count in summary['categories'].items():
            print(f"   📁 {category}: {count} 题")
        
        # 生成电子书
        print(f"\n📚 正在生成电子书...")
        
        md_file = generator.generate_markdown()
        html_file = generator.generate_html()
        
        print(f"\n✅ 电子书生成完成!")
        print(f"   📄 Markdown版本: {md_file}")
        print(f"   🌐 HTML版本: {html_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成电子书时出现错误: {e}")
        return False

def view_existing_data():
    """查看已有数据"""
    print("\n" + "="*50)
    print("查看已有数据...")
    print("="*50)
    
    raw_data_file = os.path.join(OUTPUT_CONFIG['raw_data_dir'], 'raw_data.json')
    
    if not os.path.exists(raw_data_file):
        print("❌ 未找到数据文件")
        return
    
    try:
        import json
        with open(raw_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 数据概览:")
        print(f"   总条目数: {len(data)}")
        
        if data:
            print(f"\n📝 前3条内容预览:")
            for i, item in enumerate(data[:3], 1):
                question = item.get('question', '无题目')
                answer = item.get('answer', '无答案')
                print(f"\n   {i}. 题目: {question[:50]}{'...' if len(question) > 50 else ''}")
                print(f"      答案: {answer[:80]}{'...' if len(answer) > 80 else ''}")
        
        # 检查生成的电子书
        book_dir = OUTPUT_CONFIG['book_dir']
        md_file = os.path.join(book_dir, "面试官的宝典.md")
        html_file = os.path.join(book_dir, "面试官的宝典.html")
        
        print(f"\n📚 已生成的电子书:")
        if os.path.exists(md_file):
            print(f"   ✅ Markdown版本: {md_file}")
        else:
            print(f"   ❌ Markdown版本: 未生成")
            
        if os.path.exists(html_file):
            print(f"   ✅ HTML版本: {html_file}")
        else:
            print(f"   ❌ HTML版本: 未生成")
    
    except Exception as e:
        print(f"❌ 读取数据时出现错误: {e}")

def full_process():
    """完整流程"""
    print("\n" + "="*50)
    print("执行完整流程...")
    print("="*50)
    
    # 步骤1: 抓取内容
    if not scrape_content():
        return False
    
    print("\n" + "-"*30)
    
    # 步骤2: 生成电子书
    if not generate_book():
        return False
    
    print(f"\n🎉 完整流程执行成功!")
    return True

def main():
    """主函数"""
    print_banner()
    
    # 创建必要的目录
    for dir_name in [OUTPUT_CONFIG['base_dir'], 
                    OUTPUT_CONFIG['raw_data_dir'], 
                    OUTPUT_CONFIG['processed_data_dir'],
                    OUTPUT_CONFIG['book_dir']]:
        os.makedirs(dir_name, exist_ok=True)
    
    while True:
        print_menu()
        
        try:
            choice = input("请输入选择 (1-5): ").strip()
            
            if choice == '1':
                scrape_content()
            elif choice == '2':
                generate_book()
            elif choice == '3':
                full_process()
            elif choice == '4':
                view_existing_data()
            elif choice == '5':
                print("\n👋 感谢使用，再见!")
                break
            else:
                print("\n❌ 无效选择，请输入 1-5")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，程序退出")
            break
        except Exception as e:
            print(f"\n❌ 程序出现错误: {e}")
            input("\n按回车键继续...")

if __name__ == "__main__":
    # 支持命令行参数
    parser = argparse.ArgumentParser(description='微信小程序内容抓取和电子书生成工具')
    parser.add_argument('--auto', action='store_true', help='自动执行完整流程')
    parser.add_argument('--scrape-only', action='store_true', help='仅抓取内容')
    parser.add_argument('--generate-only', action='store_true', help='仅生成电子书')
    
    args = parser.parse_args()
    
    if args.auto:
        print_banner()
        full_process()
    elif args.scrape_only:
        print_banner()
        scrape_content()
    elif args.generate_only:
        print_banner()
        generate_book()
    else:
        main()
