[{"category": "Java基础面试题", "description": "Java语言基础知识和核心概念", "questions": [{"question": "什么是Java虚拟机（JVM）？", "answer": "Java虚拟机（JVM）是运行Java字节码的虚拟机。它是Java程序的运行环境，负责将字节码转换为特定平台的机器码。JVM的主要组成部分包括：\n1. 类加载器（Class Loader）：负责加载类文件\n2. 运行时数据区：包括方法区、堆、栈、PC寄存器等\n3. 执行引擎：负责执行字节码\n4. 垃圾收集器：自动管理内存", "difficulty": "中等"}, {"question": "Java中的面向对象三大特性是什么？", "answer": "Java面向对象的三大特性是：\n1. 封装（Encapsulation）：将数据和操作数据的方法绑定在一起，隐藏内部实现细节，只暴露必要的接口\n2. 继承（Inheritance）：子类可以继承父类的属性和方法，实现代码复用\n3. 多态（Polymorphism）：同一个接口可以有多种不同的实现方式，运行时根据实际对象类型调用相应的方法", "difficulty": "基础"}, {"question": "String、StringBuffer和StringBuilder的区别？", "answer": "三者的主要区别：\n1. String：不可变对象，每次修改都会创建新对象，性能较低\n2. StringBuffer：可变对象，线程安全，适用于多线程环境\n3. StringBuilder：可变对象，线程不安全，单线程环境下性能最好\n\n使用场景：\n- 少量字符串操作：使用String\n- 多线程环境下大量字符串操作：使用StringBuffer\n- 单线程环境下大量字符串操作：使用StringBuilder", "difficulty": "中等"}]}, {"category": "Spring框架面试题", "description": "Spring框架相关知识和应用", "questions": [{"question": "解释Spring框架的核心概念", "answer": "Spring框架的核心概念包括：\n1. 控制反转（IoC）：将对象的创建和依赖关系的管理交给Spring容器\n2. 依赖注入（DI）：通过构造函数、setter方法或字段注入依赖对象\n3. 面向切面编程（AOP）：将横切关注点（如日志、事务）从业务逻辑中分离\n4. Bean管理：Spring容器管理对象的生命周期\n5. 配置管理：支持XML、注解和Java配置", "difficulty": "中等"}, {"question": "Spring Boot的优势是什么？", "answer": "Spring Boot的主要优势：\n1. 自动配置：根据项目依赖自动配置Spring应用\n2. 起步依赖：提供一系列starter依赖，简化Maven/Gradle配置\n3. 内嵌服务器：内置Tomcat、Jetty等服务器，无需外部部署\n4. 生产就绪：提供健康检查、监控、外部化配置等功能\n5. 无代码生成：不生成代码，不需要XML配置\n6. 微服务友好：天然支持微服务架构", "difficulty": "中等"}, {"question": "什么是Spring AOP？", "answer": "Spring AOP（面向切面编程）是Spring框架的重要特性：\n\n核心概念：\n1. 切面（Aspect）：横切关注点的模块化\n2. 连接点（Join Point）：程序执行的某个特定位置\n3. 切点（Pointcut）：匹配连接点的表达式\n4. 通知（Advice）：在切点执行的代码\n5. 织入（Weaving）：将切面应用到目标对象的过程\n\n应用场景：日志记录、事务管理、安全检查、性能监控等", "difficulty": "高级"}]}, {"category": "计算机网络面试题", "description": "网络协议、API设计等网络相关知识", "questions": [{"question": "什么是RESTful API？", "answer": "RESTful API是基于REST（Representational State Transfer）架构风格的API设计。其特点包括：\n1. 无状态：每个请求都包含处理该请求所需的所有信息\n2. 统一接口：使用标准的HTTP方法（GET、POST、PUT、DELETE）\n3. 资源导向：将数据和功能视为资源，通过URI标识\n4. 分层系统：支持分层架构\n5. 可缓存：响应可以被缓存以提高性能\n6. 客户端-服务器：分离用户界面和数据存储", "difficulty": "中等"}, {"question": "HTTP和HTTPS的区别？", "answer": "HTTP和HTTPS的主要区别：\n\n1. 安全性：\n   - HTTP：明文传输，数据容易被窃取\n   - HTTPS：加密传输，使用SSL/TLS协议保护数据\n\n2. 端口：\n   - HTTP：默认端口80\n   - HTTPS：默认端口443\n\n3. 证书：\n   - HTTP：不需要证书\n   - HTTPS：需要SSL证书，由CA机构颁发\n\n4. 性能：\n   - HTTP：传输速度较快\n   - HTTPS：由于加密解密，速度稍慢\n\n5. SEO：搜索引擎更偏好HTTPS网站", "difficulty": "基础"}]}, {"category": "Python开发面试题", "description": "Python语言特性和开发技巧", "questions": [{"question": "解释Python中的装饰器", "answer": "装饰器是Python中的一种设计模式，用于在不修改原函数代码的情况下扩展函数功能。装饰器本质上是一个函数，它接受一个函数作为参数并返回一个新函数。\n\n基本语法：\n```python\n@decorator\ndef function():\n    pass\n```\n\n常见用途：\n1. 日志记录\n2. 性能测试\n3. 权限验证\n4. 缓存\n5. 重试机制", "difficulty": "中等"}, {"question": "Python中的GIL是什么？", "answer": "GIL（Global Interpreter Lock）全局解释器锁是Python的一个重要概念：\n\n定义：GIL是一个互斥锁，确保同一时间只有一个线程执行Python字节码\n\n影响：\n1. 限制了多线程的并行执行\n2. CPU密集型任务无法充分利用多核\n3. I/O密集型任务影响较小\n\n解决方案：\n1. 使用多进程（multiprocessing）\n2. 使用异步编程（asyncio）\n3. 使用C扩展\n4. 考虑其他Python实现（如PyPy、Jython）", "difficulty": "高级"}, {"question": "列表推导式和生成器表达式的区别？", "answer": "列表推导式和生成器表达式的主要区别：\n\n1. 内存使用：\n   - 列表推导式：一次性创建所有元素，占用更多内存\n   - 生成器表达式：惰性求值，按需生成元素\n\n2. 语法：\n   - 列表推导式：[x for x in range(10)]\n   - 生成器表达式：(x for x in range(10))\n\n3. 返回类型：\n   - 列表推导式：返回list对象\n   - 生成器表达式：返回generator对象\n\n4. 使用场景：\n   - 小数据集或需要多次访问：列表推导式\n   - 大数据集或只需遍历一次：生成器表达式", "difficulty": "中等"}]}, {"question": "什么是数据库索引？", "answer": "数据库索引是一种数据结构，用于提高数据库查询的性能。索引类似于书籍的目录，可以快速定位到所需的数据。\n\n索引的类型：\n1. 主键索引：唯一标识表中的每一行\n2. 唯一索引：确保列中的值唯一\n3. 普通索引：提高查询性能\n4. 复合索引：基于多个列创建的索引\n5. 聚集索引：数据行的物理顺序与索引顺序相同\n6. 非聚集索引：索引顺序与数据行的物理顺序不同\n\n优点：提高查询速度\n缺点：占用额外存储空间，影响插入、更新、删除性能", "category": "数据库", "difficulty": "中等"}, {"question": "解释快速排序算法", "answer": "快速排序是一种高效的排序算法，采用分治策略。\n\n算法步骤：\n1. 选择一个基准元素（pivot）\n2. 将数组分为两部分：小于基准的元素和大于基准的元素\n3. 递归地对两部分进行快速排序\n4. 合并结果\n\n时间复杂度：\n- 最好情况：O(n log n)\n- 平均情况：O(n log n)\n- 最坏情况：O(n²)\n\n空间复杂度：O(log n)\n\n优点：平均性能优秀，原地排序\n缺点：最坏情况性能较差，不稳定排序", "category": "算法", "difficulty": "中等"}, {"question": "什么是进程和线程的区别？", "answer": "进程和线程是操作系统中的重要概念：\n\n进程（Process）：\n1. 是程序的一次执行实例\n2. 拥有独立的内存空间\n3. 进程间通信需要特殊机制（IPC）\n4. 创建和切换开销较大\n5. 一个进程崩溃不会影响其他进程\n\n线程（Thread）：\n1. 是进程内的执行单元\n2. 共享进程的内存空间\n3. 线程间通信简单（共享内存）\n4. 创建和切换开销较小\n5. 一个线程崩溃可能影响整个进程\n\n关系：一个进程可以包含多个线程", "category": "操作系统", "difficulty": "中等"}, {"question": "解释单例设计模式", "answer": "单例模式是一种创建型设计模式，确保一个类只有一个实例，并提供全局访问点。\n\n实现要点：\n1. 私有构造函数：防止外部直接创建实例\n2. 静态实例变量：保存唯一实例\n3. 静态获取方法：提供全局访问点\n\n常见实现方式：\n1. 懒汉式：延迟创建实例\n2. 饿汉式：类加载时创建实例\n3. 双重检查锁定：线程安全的懒汉式\n4. 枚举实现：最简洁的实现\n\n应用场景：\n- 数据库连接池\n- 日志记录器\n- 配置管理器\n- 缓存管理器", "category": "设计模式", "difficulty": "中等"}, {"question": "如何优化数据库查询性能？", "answer": "数据库查询性能优化的方法：\n\n1. 索引优化：\n   - 为经常查询的列创建索引\n   - 避免过多索引影响写入性能\n   - 使用复合索引优化多条件查询\n\n2. SQL语句优化：\n   - 避免SELECT *，只查询需要的列\n   - 使用WHERE子句减少数据量\n   - 避免在WHERE子句中使用函数\n   - 使用LIMIT限制结果集大小\n\n3. 表结构优化：\n   - 选择合适的数据类型\n   - 规范化和反规范化平衡\n   - 分区表处理大数据量\n\n4. 其他优化：\n   - 使用连接池\n   - 查询缓存\n   - 读写分离\n   - 数据库集群", "category": "数据库", "difficulty": "高级"}, {"question": "描述你在项目中遇到的最大挑战", "answer": "这是一个开放性问题，回答时应该包含：\n\n1. 问题描述：\n   - 具体遇到了什么技术或业务挑战\n   - 问题的复杂性和影响范围\n\n2. 解决过程：\n   - 分析问题的方法\n   - 尝试的解决方案\n   - 团队协作情况\n\n3. 最终解决方案：\n   - 采用的技术方案\n   - 实施过程\n   - 结果验证\n\n4. 经验总结：\n   - 从中学到的技术知识\n   - 项目管理经验\n   - 团队协作心得\n\n5. 后续改进：\n   - 如何避免类似问题\n   - 流程优化建议", "category": "项目经验", "difficulty": "高级"}]