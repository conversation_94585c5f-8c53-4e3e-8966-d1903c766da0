[{"question": "什么是Java虚拟机（JVM）？", "answer": "Java虚拟机（JVM）是运行Java字节码的虚拟机。它是Java程序的运行环境，负责将字节码转换为特定平台的机器码。JVM的主要组成部分包括：\n1. 类加载器（Class Loader）：负责加载类文件\n2. 运行时数据区：包括方法区、堆、栈、PC寄存器等\n3. 执行引擎：负责执行字节码\n4. 垃圾收集器：自动管理内存", "category": "Java", "difficulty": "中等"}, {"question": "解释Spring框架的核心概念", "answer": "Spring框架的核心概念包括：\n1. 控制反转（IoC）：将对象的创建和依赖关系的管理交给Spring容器\n2. 依赖注入（DI）：通过构造函数、setter方法或字段注入依赖对象\n3. 面向切面编程（AOP）：将横切关注点（如日志、事务）从业务逻辑中分离\n4. Bean管理：Spring容器管理对象的生命周期\n5. 配置管理：支持XML、注解和Java配置", "category": "Java", "difficulty": "中等"}, {"question": "什么是RESTful API？", "answer": "RESTful API是基于REST（Representational State Transfer）架构风格的API设计。其特点包括：\n1. 无状态：每个请求都包含处理该请求所需的所有信息\n2. 统一接口：使用标准的HTTP方法（GET、POST、PUT、DELETE）\n3. 资源导向：将数据和功能视为资源，通过URI标识\n4. 分层系统：支持分层架构\n5. 可缓存：响应可以被缓存以提高性能\n6. 客户端-服务器：分离用户界面和数据存储", "category": "网络", "difficulty": "中等"}, {"question": "解释Python中的装饰器", "answer": "装饰器是Python中的一种设计模式，用于在不修改原函数代码的情况下扩展函数功能。装饰器本质上是一个函数，它接受一个函数作为参数并返回一个新函数。\n\n基本语法：\n```python\n@decorator\ndef function():\n    pass\n```\n\n常见用途：\n1. 日志记录\n2. 性能测试\n3. 权限验证\n4. 缓存\n5. 重试机制", "category": "Python", "difficulty": "中等"}, {"question": "什么是数据库索引？", "answer": "数据库索引是一种数据结构，用于提高数据库查询的性能。索引类似于书籍的目录，可以快速定位到所需的数据。\n\n索引的类型：\n1. 主键索引：唯一标识表中的每一行\n2. 唯一索引：确保列中的值唯一\n3. 普通索引：提高查询性能\n4. 复合索引：基于多个列创建的索引\n5. 聚集索引：数据行的物理顺序与索引顺序相同\n6. 非聚集索引：索引顺序与数据行的物理顺序不同\n\n优点：提高查询速度\n缺点：占用额外存储空间，影响插入、更新、删除性能", "category": "数据库", "difficulty": "中等"}, {"question": "解释快速排序算法", "answer": "快速排序是一种高效的排序算法，采用分治策略。\n\n算法步骤：\n1. 选择一个基准元素（pivot）\n2. 将数组分为两部分：小于基准的元素和大于基准的元素\n3. 递归地对两部分进行快速排序\n4. 合并结果\n\n时间复杂度：\n- 最好情况：O(n log n)\n- 平均情况：O(n log n)\n- 最坏情况：O(n²)\n\n空间复杂度：O(log n)\n\n优点：平均性能优秀，原地排序\n缺点：最坏情况性能较差，不稳定排序", "category": "算法", "difficulty": "中等"}, {"question": "什么是进程和线程的区别？", "answer": "进程和线程是操作系统中的重要概念：\n\n进程（Process）：\n1. 是程序的一次执行实例\n2. 拥有独立的内存空间\n3. 进程间通信需要特殊机制（IPC）\n4. 创建和切换开销较大\n5. 一个进程崩溃不会影响其他进程\n\n线程（Thread）：\n1. 是进程内的执行单元\n2. 共享进程的内存空间\n3. 线程间通信简单（共享内存）\n4. 创建和切换开销较小\n5. 一个线程崩溃可能影响整个进程\n\n关系：一个进程可以包含多个线程", "category": "操作系统", "difficulty": "中等"}, {"question": "解释单例设计模式", "answer": "单例模式是一种创建型设计模式，确保一个类只有一个实例，并提供全局访问点。\n\n实现要点：\n1. 私有构造函数：防止外部直接创建实例\n2. 静态实例变量：保存唯一实例\n3. 静态获取方法：提供全局访问点\n\n常见实现方式：\n1. 懒汉式：延迟创建实例\n2. 饿汉式：类加载时创建实例\n3. 双重检查锁定：线程安全的懒汉式\n4. 枚举实现：最简洁的实现\n\n应用场景：\n- 数据库连接池\n- 日志记录器\n- 配置管理器\n- 缓存管理器", "category": "设计模式", "difficulty": "中等"}, {"question": "如何优化数据库查询性能？", "answer": "数据库查询性能优化的方法：\n\n1. 索引优化：\n   - 为经常查询的列创建索引\n   - 避免过多索引影响写入性能\n   - 使用复合索引优化多条件查询\n\n2. SQL语句优化：\n   - 避免SELECT *，只查询需要的列\n   - 使用WHERE子句减少数据量\n   - 避免在WHERE子句中使用函数\n   - 使用LIMIT限制结果集大小\n\n3. 表结构优化：\n   - 选择合适的数据类型\n   - 规范化和反规范化平衡\n   - 分区表处理大数据量\n\n4. 其他优化：\n   - 使用连接池\n   - 查询缓存\n   - 读写分离\n   - 数据库集群", "category": "数据库", "difficulty": "高级"}, {"question": "描述你在项目中遇到的最大挑战", "answer": "这是一个开放性问题，回答时应该包含：\n\n1. 问题描述：\n   - 具体遇到了什么技术或业务挑战\n   - 问题的复杂性和影响范围\n\n2. 解决过程：\n   - 分析问题的方法\n   - 尝试的解决方案\n   - 团队协作情况\n\n3. 最终解决方案：\n   - 采用的技术方案\n   - 实施过程\n   - 结果验证\n\n4. 经验总结：\n   - 从中学到的技术知识\n   - 项目管理经验\n   - 团队协作心得\n\n5. 后续改进：\n   - 如何避免类似问题\n   - 流程优化建议", "category": "项目经验", "difficulty": "高级"}]