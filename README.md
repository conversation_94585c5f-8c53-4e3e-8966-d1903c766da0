# 微信小程序《面试官的宝典》抓取工具

这是一个用于抓取微信小程序《面试官的宝典》内容并生成电子书的工具。

## 功能特性

- 🔍 多策略内容抓取（网页版搜索、API发现、手动输入）
- 📊 智能内容分类和整理
- 📚 多格式电子书生成（Markdown、HTML）
- 🎯 面试题自动分类（Java、Python、算法等）
- 📈 数据统计和分析

## 技术说明

由于微信小程序的技术限制，直接抓取存在以下挑战：

1. **小程序环境限制**: 小程序运行在微信客户端内，无法直接通过常规爬虫访问
2. **协议限制**: `#小程序://面试官的宝典/3YCUMT4m1hkQkix` 是微信内部协议
3. **反爬虫机制**: 小程序可能有防护措施

## 解决方案

本工具提供多种抓取策略：

### 策略1: 网页版搜索
- 自动搜索是否存在对应的网页版本
- 如果找到，直接抓取网页内容

### 策略2: API端点发现
- 尝试发现小程序后端API
- 通过API接口获取数据

### 策略3: 手动输入模式
- 提供友好的手动输入界面
- 支持批量输入面试题和答案
- 自动格式化和分类

## 安装和使用

### 1. 环境准备

```bash
# 克隆或下载项目
cd 微信公众号整理

# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 运行程序

#### 交互式模式（推荐）
```bash
python main.py
```

#### 命令行模式
```bash
# 执行完整流程
python main.py --auto

# 仅抓取内容
python main.py --scrape-only

# 仅生成电子书
python main.py --generate-only
```

### 3. 手动输入数据

如果自动抓取失败，程序会进入手动输入模式：

```
题目: 什么是Java虚拟机？
答案: Java虚拟机（JVM）是运行Java字节码的虚拟机。它是Java程序的运行环境，负责将字节码转换为特定平台的机器码。
---
题目: 解释Spring框架的核心概念
答案: Spring框架的核心概念包括：1. 控制反转（IoC）2. 依赖注入（DI）3. 面向切面编程（AOP）...
---
quit
```

## 输出文件

程序会在以下目录生成文件：

```
output/
├── raw_data/
│   └── raw_data.json          # 原始抓取数据
├── processed_data/
│   └── summary.json           # 数据统计摘要
└── book/
    ├── 面试官的宝典.md        # Markdown电子书
    └── 面试官的宝典.html      # HTML电子书
```

## 配置说明

可以在 `config.py` 中修改配置：

```python
# 小程序信息
MINIPROGRAM_INFO = {
    "name": "面试官的宝典",
    "path": "#小程序://面试官的宝典/3YCUMT4m1hkQkix"
}

# 抓取配置
SCRAPING_CONFIG = {
    "delay_between_requests": 2,  # 请求间隔
    "max_retries": 3,            # 最大重试次数
    "timeout": 30                # 请求超时时间
}

# 书籍配置
BOOK_CONFIG = {
    "title": "面试官的宝典 - 完整版",
    "author": "微信小程序整理"
}
```

## 自动分类功能

程序会根据问题内容自动分类：

- **Java**: Spring、JVM、MyBatis等
- **Python**: Django、Flask、数据分析等
- **JavaScript**: Vue、React、Node.js等
- **数据库**: MySQL、Redis、MongoDB等
- **算法**: 数据结构、排序、动态规划等
- **网络**: HTTP、TCP/IP、RESTful等
- **操作系统**: Linux、进程、线程等

## 高级用法

### 1. 批量处理多个数据文件

```python
from book_generator import BookGenerator

generator = BookGenerator()
generator.load_data('data1.json')
generator.load_data('data2.json')  # 追加数据
generator.generate_markdown()
```

### 2. 自定义分类规则

修改 `book_generator.py` 中的 `auto_categorize` 方法来自定义分类逻辑。

### 3. 添加新的输出格式

继承 `BookGenerator` 类并添加新的生成方法：

```python
def generate_pdf(self):
    # PDF生成逻辑
    pass
```

## 注意事项

1. **版权声明**: 请确保遵守相关版权法律，仅用于个人学习目的
2. **数据准确性**: 手动输入的数据请仔细核对
3. **网络环境**: 某些功能需要稳定的网络连接
4. **存储空间**: 确保有足够的磁盘空间存储生成的文件

## 故障排除

### 常见问题

1. **导入错误**: 确保已安装所有依赖包
2. **编码问题**: 确保终端支持UTF-8编码
3. **权限问题**: 确保有写入文件的权限

### 获取帮助

如果遇到问题，可以：

1. 检查错误日志
2. 确认配置文件正确
3. 尝试重新安装依赖

## 更新日志

- **v1.0.0**: 初始版本，支持基本抓取和电子书生成功能

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
