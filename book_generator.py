#!/usr/bin/env python3
"""
电子书生成器

将抓取到的面试题数据整理成电子书格式
支持多种输出格式：Markdown, HTML, PDF等
"""

import json
import os
import re
from datetime import datetime
from config import OUTPUT_CONFIG, BOOK_CONFIG

class BookGenerator:
    def __init__(self):
        self.data = []
        self.processed_data = {}
        
    def load_data(self, filepath):
        """加载原始数据"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 条数据")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def categorize_questions(self):
        """对问题进行分类整理"""
        categories = {}

        # 检查数据格式：新格式（按小程序功能入口分类）还是旧格式
        if self.data and isinstance(self.data[0], dict) and 'questions' in self.data[0]:
            # 新格式：每个元素是一个分类，包含多个问题
            for category_data in self.data:
                category_name = category_data.get('category', '其他')
                category_desc = category_data.get('description', '')
                category_icon = category_data.get('icon', '📝')
                questions_list = category_data.get('questions', [])

                categories[category_name] = {
                    'description': category_desc,
                    'icon': category_icon,
                    'questions': []
                }

                for question_item in questions_list:
                    categories[category_name]['questions'].append({
                        'question': question_item.get('question', ''),
                        'answer': question_item.get('answer', ''),
                        'difficulty': question_item.get('difficulty', '中等'),
                        'tags': question_item.get('tags', [])
                    })
        else:
            # 旧格式：每个元素是一个问题
            for item in self.data:
                question = item.get('question', '')
                answer = item.get('answer', '')
                category = item.get('category', '其他')

                # 自动分类逻辑
                if not category or category == '面试题':
                    category = self.auto_categorize(question)

                if category not in categories:
                    categories[category] = {
                        'description': '',
                        'icon': '📝',
                        'questions': []
                    }

                categories[category]['questions'].append({
                    'question': question,
                    'answer': answer,
                    'difficulty': item.get('difficulty', '中等'),
                    'tags': item.get('tags', [])
                })

        self.processed_data = categories
        return categories
    
    def auto_categorize(self, question):
        """自动分类问题"""
        question_lower = question.lower()
        
        # 技术分类关键词
        tech_keywords = {
            'Java': ['java', 'jvm', 'spring', 'springboot', 'mybatis', 'hibernate'],
            'Python': ['python', 'django', 'flask', 'pandas', 'numpy'],
            'JavaScript': ['javascript', 'js', 'vue', 'react', 'angular', 'node'],
            '数据库': ['数据库', 'mysql', 'redis', 'mongodb', 'sql', 'oracle'],
            '算法': ['算法', '数据结构', '排序', '查找', '树', '图', '动态规划'],
            '网络': ['网络', 'http', 'tcp', 'ip', 'socket', 'restful'],
            '操作系统': ['操作系统', 'linux', 'windows', '进程', '线程', '内存'],
            '设计模式': ['设计模式', '单例', '工厂', '观察者', '策略'],
            '项目经验': ['项目', '经验', '团队', '管理', '沟通'],
            '基础知识': ['基础', '概念', '原理', '定义']
        }
        
        for category, keywords in tech_keywords.items():
            for keyword in keywords:
                if keyword in question_lower:
                    return category
        
        return '其他'
    
    def generate_markdown(self):
        """生成Markdown格式的电子书"""
        if not self.processed_data:
            self.categorize_questions()
        
        markdown_content = []
        
        # 书籍标题和信息
        markdown_content.append(f"# {BOOK_CONFIG['title']}\n")
        markdown_content.append(f"**作者**: {BOOK_CONFIG['author']}\n")
        markdown_content.append(f"**描述**: {BOOK_CONFIG['description']}\n")
        markdown_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        markdown_content.append("---\n")
        
        # 目录
        markdown_content.append("## 📚 目录\n")
        for i, (category, category_data) in enumerate(self.processed_data.items(), 1):
            icon = category_data.get('icon', '📝')
            description = category_data.get('description', '')
            question_count = len(category_data.get('questions', []))
            markdown_content.append(f"{i}. {icon} [{category}](#{category.replace(' ', '-').lower()}) ({question_count}题)")
            if description:
                markdown_content.append(f" - {description}")
            markdown_content.append("\n")
        markdown_content.append("\n---\n")

        # 各章节内容
        for category, category_data in self.processed_data.items():
            icon = category_data.get('icon', '📝')
            description = category_data.get('description', '')
            questions = category_data.get('questions', [])

            markdown_content.append(f"\n## {icon} {category}\n")
            if description:
                markdown_content.append(f"*{description}*\n")
            markdown_content.append(f"\n**本章共 {len(questions)} 道题目**\n")

            for i, item in enumerate(questions, 1):
                markdown_content.append(f"\n### {i}. {item['question']}\n")

                # 添加标签
                if item.get('tags'):
                    tags_str = " ".join([f"`{tag}`" for tag in item['tags']])
                    markdown_content.append(f"**标签**: {tags_str}\n")

                markdown_content.append(f"**难度**: {item['difficulty']}\n")
                markdown_content.append(f"\n**答案**:\n")
                markdown_content.append(f"{item['answer']}\n")
                markdown_content.append("\n---\n")
        
        # 确保目录存在
        os.makedirs(OUTPUT_CONFIG['book_dir'], exist_ok=True)

        # 保存文件
        filename = "面试官的宝典.md"
        filepath = os.path.join(OUTPUT_CONFIG['book_dir'], filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
        
        print(f"Markdown电子书已生成: {filepath}")
        return filepath
    
    def generate_html(self):
        """生成HTML格式的电子书"""
        if not self.processed_data:
            self.categorize_questions()
        
        html_content = []
        
        # HTML头部
        html_content.append("""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{}</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 40px; background-color: #f8f9fa; }}
        .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; text-align: center; }}
        h2 {{ color: #34495e; border-left: 4px solid #3498db; padding-left: 15px; margin-top: 30px; }}
        h3 {{ color: #2c3e50; margin-bottom: 10px; }}
        .question {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
        .answer {{ background-color: #e8f5e8; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #27ae60; }}
        .difficulty {{ color: #e74c3c; font-weight: bold; background-color: rgba(231, 76, 60, 0.1); padding: 4px 8px; border-radius: 4px; display: inline-block; }}
        .toc {{ background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 25px; border-radius: 10px; margin-bottom: 30px; }}
        .toc ul {{ list-style-type: none; padding: 0; }}
        .toc li {{ margin: 10px 0; padding: 8px; background-color: rgba(255,255,255,0.1); border-radius: 5px; }}
        .toc a {{ text-decoration: none; color: white; font-weight: 500; }}
        .toc a:hover {{ text-decoration: underline; }}
        .question-count {{ color: #ddd; font-size: 0.9em; margin-left: 10px; }}
        .category-desc {{ color: #ddd; font-style: italic; }}
        .category-description {{ color: #7f8c8d; font-style: italic; margin-bottom: 15px; }}
        .chapter-info {{ background-color: #3498db; color: white; padding: 10px; border-radius: 5px; text-align: center; margin-bottom: 20px; }}
        .tags {{ margin: 10px 0; }}
        .tag {{ background-color: #e74c3c; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; margin-right: 5px; display: inline-block; }}
        .question h3 {{ margin-top: 0; color: white; }}
        .answer strong {{ color: #27ae60; }}
    </style>
</head>
<body>""".format(BOOK_CONFIG['title']))
        
        # 书籍信息
        html_content.append('<div class="container">')
        html_content.append(f"<h1>{BOOK_CONFIG['title']}</h1>")
        html_content.append(f"<p><strong>作者</strong>: {BOOK_CONFIG['author']}</p>")
        html_content.append(f"<p><strong>描述</strong>: {BOOK_CONFIG['description']}</p>")
        html_content.append(f"<p><strong>生成时间</strong>: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>")
        
        # 目录
        html_content.append('<div class="toc">')
        html_content.append('<h2>📚 目录</h2>')
        html_content.append('<ul>')
        for category, category_data in self.processed_data.items():
            anchor = category.replace(' ', '-').lower()
            icon = category_data.get('icon', '📝')
            description = category_data.get('description', '')
            question_count = len(category_data.get('questions', []))
            html_content.append(f'<li>')
            html_content.append(f'<a href="#{anchor}">{icon} {category}</a>')
            html_content.append(f'<span class="question-count">({question_count}题)</span>')
            if description:
                html_content.append(f'<br><small class="category-desc">{description}</small>')
            html_content.append(f'</li>')
        html_content.append('</ul>')
        html_content.append('</div>')

        # 各章节内容
        for category, category_data in self.processed_data.items():
            anchor = category.replace(' ', '-').lower()
            icon = category_data.get('icon', '📝')
            description = category_data.get('description', '')
            questions = category_data.get('questions', [])

            html_content.append(f'<h2 id="{anchor}">{icon} {category}</h2>')
            if description:
                html_content.append(f'<p class="category-description"><em>{description}</em></p>')
            html_content.append(f'<p class="chapter-info">本章共 <strong>{len(questions)}</strong> 道题目</p>')

            for i, item in enumerate(questions, 1):
                html_content.append(f'<div class="question">')
                html_content.append(f'<h3>{i}. {item["question"]}</h3>')

                # 添加标签
                if item.get('tags'):
                    html_content.append('<div class="tags">')
                    for tag in item['tags']:
                        html_content.append(f'<span class="tag">{tag}</span>')
                    html_content.append('</div>')

                html_content.append(f'<p class="difficulty">难度: {item["difficulty"]}</p>')
                html_content.append(f'</div>')

                html_content.append(f'<div class="answer">')
                html_content.append(f'<strong>答案:</strong><br>')
                html_content.append(f'{item["answer"].replace(chr(10), "<br>")}')
                html_content.append(f'</div>')
        
        html_content.append('</div>')  # 关闭container
        html_content.append('</body></html>')

        # 确保目录存在
        os.makedirs(OUTPUT_CONFIG['book_dir'], exist_ok=True)

        # 保存文件
        filename = "面试官的宝典.html"
        filepath = os.path.join(OUTPUT_CONFIG['book_dir'], filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(html_content))
        
        print(f"HTML电子书已生成: {filepath}")
        return filepath
    
    def generate_summary(self):
        """生成数据统计摘要"""
        if not self.processed_data:
            self.categorize_questions()

        total_questions = 0
        summary = {
            'total_questions': 0,
            'categories': {},
            'difficulty_distribution': {},
            'tag_distribution': {}
        }

        for category, category_data in self.processed_data.items():
            questions = category_data.get('questions', [])
            question_count = len(questions)
            total_questions += question_count

            summary['categories'][category] = {
                'count': question_count,
                'description': category_data.get('description', ''),
                'icon': category_data.get('icon', '📝')
            }

            for item in questions:
                # 统计难度分布
                difficulty = item['difficulty']
                if difficulty not in summary['difficulty_distribution']:
                    summary['difficulty_distribution'][difficulty] = 0
                summary['difficulty_distribution'][difficulty] += 1

                # 统计标签分布
                tags = item.get('tags', [])
                for tag in tags:
                    if tag not in summary['tag_distribution']:
                        summary['tag_distribution'][tag] = 0
                    summary['tag_distribution'][tag] += 1

        summary['total_questions'] = total_questions

        # 确保目录存在
        os.makedirs(OUTPUT_CONFIG['processed_data_dir'], exist_ok=True)

        # 保存摘要
        summary_file = os.path.join(OUTPUT_CONFIG['processed_data_dir'], 'summary.json')
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        print(f"数据摘要已生成: {summary_file}")
        return summary

def main():
    """主函数"""
    print("电子书生成器")
    print("="*50)
    
    generator = BookGenerator()
    
    # 查找原始数据文件
    raw_data_file = os.path.join(OUTPUT_CONFIG['raw_data_dir'], 'raw_data.json')
    
    if not os.path.exists(raw_data_file):
        print(f"未找到数据文件: {raw_data_file}")
        print("请先运行 miniprogram_scraper.py 抓取数据")
        return
    
    # 加载数据
    if not generator.load_data(raw_data_file):
        return
    
    # 生成电子书
    try:
        # 生成统计摘要
        summary = generator.generate_summary()
        print(f"\n数据统计:")
        print(f"总题目数: {summary['total_questions']}")
        print(f"分类数: {len(summary['categories'])}")
        for category, count in summary['categories'].items():
            print(f"  {category}: {count} 题")
        
        # 生成Markdown版本
        md_file = generator.generate_markdown()
        
        # 生成HTML版本
        html_file = generator.generate_html()
        
        print(f"\n电子书生成完成!")
        print(f"Markdown版本: {md_file}")
        print(f"HTML版本: {html_file}")
        
    except Exception as e:
        print(f"生成电子书时出现错误: {e}")

if __name__ == "__main__":
    main()
