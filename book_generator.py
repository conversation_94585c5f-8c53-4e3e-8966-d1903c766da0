#!/usr/bin/env python3
"""
电子书生成器

将抓取到的面试题数据整理成电子书格式
支持多种输出格式：Markdown, HTML, PDF等
"""

import json
import os
import re
from datetime import datetime
from config import OUTPUT_CONFIG, BOOK_CONFIG

class BookGenerator:
    def __init__(self):
        self.data = []
        self.processed_data = {}
        
    def load_data(self, filepath):
        """加载原始数据"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 条数据")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def categorize_questions(self):
        """对问题进行分类整理"""
        categories = {}
        
        for item in self.data:
            question = item.get('question', '')
            answer = item.get('answer', '')
            category = item.get('category', '其他')
            
            # 自动分类逻辑
            if not category or category == '面试题':
                category = self.auto_categorize(question)
            
            if category not in categories:
                categories[category] = []
            
            categories[category].append({
                'question': question,
                'answer': answer,
                'difficulty': item.get('difficulty', '中等')
            })
        
        self.processed_data = categories
        return categories
    
    def auto_categorize(self, question):
        """自动分类问题"""
        question_lower = question.lower()
        
        # 技术分类关键词
        tech_keywords = {
            'Java': ['java', 'jvm', 'spring', 'springboot', 'mybatis', 'hibernate'],
            'Python': ['python', 'django', 'flask', 'pandas', 'numpy'],
            'JavaScript': ['javascript', 'js', 'vue', 'react', 'angular', 'node'],
            '数据库': ['数据库', 'mysql', 'redis', 'mongodb', 'sql', 'oracle'],
            '算法': ['算法', '数据结构', '排序', '查找', '树', '图', '动态规划'],
            '网络': ['网络', 'http', 'tcp', 'ip', 'socket', 'restful'],
            '操作系统': ['操作系统', 'linux', 'windows', '进程', '线程', '内存'],
            '设计模式': ['设计模式', '单例', '工厂', '观察者', '策略'],
            '项目经验': ['项目', '经验', '团队', '管理', '沟通'],
            '基础知识': ['基础', '概念', '原理', '定义']
        }
        
        for category, keywords in tech_keywords.items():
            for keyword in keywords:
                if keyword in question_lower:
                    return category
        
        return '其他'
    
    def generate_markdown(self):
        """生成Markdown格式的电子书"""
        if not self.processed_data:
            self.categorize_questions()
        
        markdown_content = []
        
        # 书籍标题和信息
        markdown_content.append(f"# {BOOK_CONFIG['title']}\n")
        markdown_content.append(f"**作者**: {BOOK_CONFIG['author']}\n")
        markdown_content.append(f"**描述**: {BOOK_CONFIG['description']}\n")
        markdown_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        markdown_content.append("---\n")
        
        # 目录
        markdown_content.append("## 目录\n")
        for i, category in enumerate(self.processed_data.keys(), 1):
            markdown_content.append(f"{i}. [{category}](#{category.replace(' ', '-').lower()})\n")
        markdown_content.append("\n---\n")
        
        # 各章节内容
        for category, questions in self.processed_data.items():
            markdown_content.append(f"\n## {category}\n")
            
            for i, item in enumerate(questions, 1):
                markdown_content.append(f"\n### {i}. {item['question']}\n")
                markdown_content.append(f"**难度**: {item['difficulty']}\n")
                markdown_content.append(f"**答案**:\n")
                markdown_content.append(f"{item['answer']}\n")
                markdown_content.append("---\n")
        
        # 确保目录存在
        os.makedirs(OUTPUT_CONFIG['book_dir'], exist_ok=True)

        # 保存文件
        filename = "面试官的宝典.md"
        filepath = os.path.join(OUTPUT_CONFIG['book_dir'], filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
        
        print(f"Markdown电子书已生成: {filepath}")
        return filepath
    
    def generate_html(self):
        """生成HTML格式的电子书"""
        if not self.processed_data:
            self.categorize_questions()
        
        html_content = []
        
        # HTML头部
        html_content.append("""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{}</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 40px; }}
        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; border-left: 4px solid #3498db; padding-left: 15px; }}
        h3 {{ color: #2c3e50; }}
        .question {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }}
        .answer {{ background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; }}
        .difficulty {{ color: #e74c3c; font-weight: bold; }}
        .toc {{ background-color: #ecf0f1; padding: 20px; border-radius: 5px; }}
        .toc ul {{ list-style-type: none; }}
        .toc a {{ text-decoration: none; color: #2980b9; }}
        .toc a:hover {{ text-decoration: underline; }}
    </style>
</head>
<body>""".format(BOOK_CONFIG['title']))
        
        # 书籍信息
        html_content.append(f"<h1>{BOOK_CONFIG['title']}</h1>")
        html_content.append(f"<p><strong>作者</strong>: {BOOK_CONFIG['author']}</p>")
        html_content.append(f"<p><strong>描述</strong>: {BOOK_CONFIG['description']}</p>")
        html_content.append(f"<p><strong>生成时间</strong>: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>")
        
        # 目录
        html_content.append('<div class="toc">')
        html_content.append('<h2>目录</h2>')
        html_content.append('<ul>')
        for category in self.processed_data.keys():
            anchor = category.replace(' ', '-').lower()
            html_content.append(f'<li><a href="#{anchor}">{category}</a></li>')
        html_content.append('</ul>')
        html_content.append('</div>')
        
        # 各章节内容
        for category, questions in self.processed_data.items():
            anchor = category.replace(' ', '-').lower()
            html_content.append(f'<h2 id="{anchor}">{category}</h2>')
            
            for i, item in enumerate(questions, 1):
                html_content.append(f'<div class="question">')
                html_content.append(f'<h3>{i}. {item["question"]}</h3>')
                html_content.append(f'<p class="difficulty">难度: {item["difficulty"]}</p>')
                html_content.append(f'</div>')
                
                html_content.append(f'<div class="answer">')
                html_content.append(f'<strong>答案:</strong><br>')
                html_content.append(f'{item["answer"].replace(chr(10), "<br>")}')
                html_content.append(f'</div>')
        
        html_content.append('</body></html>')

        # 确保目录存在
        os.makedirs(OUTPUT_CONFIG['book_dir'], exist_ok=True)

        # 保存文件
        filename = "面试官的宝典.html"
        filepath = os.path.join(OUTPUT_CONFIG['book_dir'], filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(html_content))
        
        print(f"HTML电子书已生成: {filepath}")
        return filepath
    
    def generate_summary(self):
        """生成数据统计摘要"""
        if not self.processed_data:
            self.categorize_questions()

        summary = {
            'total_questions': len(self.data),
            'categories': {},
            'difficulty_distribution': {}
        }

        for category, questions in self.processed_data.items():
            summary['categories'][category] = len(questions)

            for item in questions:
                difficulty = item['difficulty']
                if difficulty not in summary['difficulty_distribution']:
                    summary['difficulty_distribution'][difficulty] = 0
                summary['difficulty_distribution'][difficulty] += 1

        # 确保目录存在
        os.makedirs(OUTPUT_CONFIG['processed_data_dir'], exist_ok=True)

        # 保存摘要
        summary_file = os.path.join(OUTPUT_CONFIG['processed_data_dir'], 'summary.json')
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        print(f"数据摘要已生成: {summary_file}")
        return summary

def main():
    """主函数"""
    print("电子书生成器")
    print("="*50)
    
    generator = BookGenerator()
    
    # 查找原始数据文件
    raw_data_file = os.path.join(OUTPUT_CONFIG['raw_data_dir'], 'raw_data.json')
    
    if not os.path.exists(raw_data_file):
        print(f"未找到数据文件: {raw_data_file}")
        print("请先运行 miniprogram_scraper.py 抓取数据")
        return
    
    # 加载数据
    if not generator.load_data(raw_data_file):
        return
    
    # 生成电子书
    try:
        # 生成统计摘要
        summary = generator.generate_summary()
        print(f"\n数据统计:")
        print(f"总题目数: {summary['total_questions']}")
        print(f"分类数: {len(summary['categories'])}")
        for category, count in summary['categories'].items():
            print(f"  {category}: {count} 题")
        
        # 生成Markdown版本
        md_file = generator.generate_markdown()
        
        # 生成HTML版本
        html_file = generator.generate_html()
        
        print(f"\n电子书生成完成!")
        print(f"Markdown版本: {md_file}")
        print(f"HTML版本: {html_file}")
        
    except Exception as e:
        print(f"生成电子书时出现错误: {e}")

if __name__ == "__main__":
    main()
